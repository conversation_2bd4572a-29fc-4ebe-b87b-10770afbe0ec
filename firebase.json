{"firestore": {"database": "(default)", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "optimeal/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}, "singleProjectMode": true}}
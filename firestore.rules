rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Users can read and write ONLY their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Recipes can be read by anyone authenticated, but only created by the user
    match /recipes/{recipeId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.resource.data.authorId == request.auth.uid;
    }
  }
}

.navbar {
  min-width: fit-content;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 32px;
  background-color: #ffffff; 
  color: rgb(0, 0, 0); 
  border-radius: none; 
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.navbar h1 {
  font-size: 24px;
  margin: 0;
  color: rgb(0, 0, 0);
}
.navbar nav a {
  margin-left: 16px;
  padding: 6px 14px;
  color: #000000;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}
.dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 32px;
  background-color: #E7DECD;
}
.branding {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card {
  min-width: fit-content;
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

/* Weekly Meal Plan Table Styling */
.weekly-meal-plan {
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transition: transform 0.2s ease;
}

.weekly-meal-plan:hover {
  transform: translateY(-5px);
}

.weekly-meal-plan h2 {
  font-size: 1.6rem;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
  position: relative;
}

.weekly-meal-plan h2:after {
  content: "";
  display: block;
  width: 50px;
  height: 3px;
  background-color: #3498db;
  margin: 8px auto 0;
  border-radius: 2px;
}

.weekly-meal-plan table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.weekly-meal-plan th, 
.weekly-meal-plan td {
  padding: 14px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.weekly-meal-plan th {
  background-color: #f5f9ff;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.weekly-meal-plan tr:last-child td {
  border-bottom: none;
}

.weekly-meal-plan td:first-child {
  font-weight: 600;
  color: #3498db;
  width: 100px;
}

.weekly-meal-plan tr:hover td {
  background-color: #f8f9fa;
}

.generate-btn {
  width: 200px;
  padding: 12px 16px;
  margin-top: 20px;
  font-size: 14px;
  border: none;
  border-radius: 30px;
  background: linear-gradient(135deg, #4CAF50, #2E8B57);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(76, 175, 80, 0.2);
  display: block;
  margin-left: auto;
  margin-right: auto;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.generate-btn:hover {
  background: linear-gradient(135deg, #43A047, #2E7D32);
  box-shadow: 0 6px 15px rgba(76, 175, 80, 0.3);
  transform: translateY(-2px);
}

/* Nutritional Overview Styling */
.nutrition-overview {
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transition: transform 0.2s ease;
}

.nutrition-overview:hover {
  transform: translateY(-5px);
}

.nutrition-overview h2 {
  font-size: 1.6rem;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
  position: relative;
}

.nutrition-overview h2:after {
  content: "";
  display: block;
  width: 50px;
  height: 3px;
  background-color: #f39c12;
  margin: 8px auto 0;
  border-radius: 2px;
}

.nutrition-stats {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.stat {
  flex: 1;
  background: linear-gradient(to bottom, #f9f9f9, #f1f1f1);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat p {
  margin: 0 0 5px 0;
  color: #7f8c8d;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat strong {
  display: block;
  font-size: 1.8rem;
  color: #2c3e50;
  margin: 5px 0;
}

.stat span {
  font-size: 0.8rem;
  color: #95a5a6;
}

.avatar {
  text-align: center;
}

.avatar img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  
}

/* Profile Card Styling */
.profile {
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  transition: transform 0.2s ease;
}

.profile:hover {
  transform: translateY(-5px);
}

.profile-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.profile-header h2 {
  font-size: 1.6rem;
  color: #2c3e50;
  position: relative;
}

.profile-header h2:after {
  content: "";
  display: block;
  width: 50px;
  height: 3px;
  background-color: #e74c3c;
  margin: 8px auto 0;
  border-radius: 2px;
}

#profile-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

#profile-content p {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 0;
  transition: background-color 0.2s ease;
}

#profile-content p:hover {
  background-color: #f0f2f5;
}

#profile-content p span {
  font-weight: 600;
  color: #34495e;
}

/* Profile Edit Styling */
#profile-edit {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

#profile-edit p {
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

#profile-edit label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 5px 0;
  cursor: pointer;
  transition: color 0.2s ease;
}

#profile-edit label:hover {
  color: #3498db;
}

#profile-edit input[type="text"],
#profile-edit input[type="number"],
#profile-edit select {
  padding: 10px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.95rem;
  background-color: #f8f9fa;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

#profile-edit input[type="text"]:focus,
#profile-edit input[type="number"]:focus,
#profile-edit select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  outline: none;
}

#profile-edit input[type="checkbox"] {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  cursor: pointer;
}

#profile-edit p > span:first-child {
  font-weight: 600;
  color: #34495e;
  margin-bottom: 5px;
}

.edit-btn {
  width: 200px;
  padding: 12px 16px;
  margin-top: 20px;
  font-size: 14px;
  border: none;
  border-radius: 30px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
  display: block;
  margin-left: auto;
  margin-right: auto;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #2980b9, #2471a3);
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
  transform: translateY(-2px);
}

.save-btn {
  width: 200px;
  padding: 12px 16px;
  margin-top: 20px;
  font-size: 14px;
  border: none;
  border-radius: 30px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
  display: block;
  margin-left: auto;
  margin-right: auto;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.save-btn:hover {
  background: linear-gradient(135deg, #2980b9, #2471a3);
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
  transform: translateY(-2px);
}

.logout-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  margin-left: 1rem;
  border-radius: 5px;
  cursor: pointer;
  width: auto;
}

.logout-btn:hover {
  background-color: #c0392b;
}

html, body{
  margin: 0;
  padding: 0;
  background-color: #E7DECD; /* same as .dashboard for consistency */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.explore-container {
  padding: 1rem;
}

.explore-container h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
}

.explore-container h3:after {
  content: "";
  display: block;
  width: 50px;
  height: 3px;
  background-color: #3498db;
  margin: 10px auto 0;
  border-radius: 2px;
}

.recipe-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 4rem;
  margin-top: 2rem;
}

.recipe-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  height: 100%;
}

.recipe-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border-color: #e0e0e0;
}

.recipe-content {
  flex: 1;
}

.recipe-content h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1.3rem;
  color: #2c3e50;
  line-height: 1.3;
}

.recipe-content p {
  font-size: 0.95rem;
  color: #7f8c8d;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.recipe-footer {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #f0f0f0;
}

.recipe-footer span {
  font-size: 0.85rem;
  color: #95a5a6;
  font-style: italic;
}

.recipe-footer button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.recipe-footer button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

.recipe-footer button:active {
  transform: translateY(0);
}

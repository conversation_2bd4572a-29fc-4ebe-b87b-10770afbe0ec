/* Saved Recipes Container */
.saved-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
}

/* Page Title */
h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1.5rem;
  text-align: center;
  position: relative;
}

h3:after {
  content: "";
  display: block;
  width: 50px;
  height: 3px;
  background-color: #e74c3c;
  margin: 10px auto 0;
  border-radius: 2px;
}

/* Recipe Card Styling */
.recipe-card {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  background-color: #fff;
  word-wrap: break-word;
  overflow-wrap: break-word;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.recipe-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border-color: #e0e0e0;
}

.recipe-card h4 {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  color: #2c3e50;
  line-height: 1.3;
}

.recipe-card p {
  font-size: 0.95rem;
  color: #7f8c8d;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex: 1;
}

.recipe-card a {
  display: inline-block;
  color: #3498db;
  text-decoration: none;
  margin-top: 0.75rem;
  word-break: break-all;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.recipe-card a:hover {
  color: #2980b9;
  text-decoration: underline;
}

.recipe-card button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  align-self: flex-end;
  font-weight: 500;
}

.recipe-card button:hover {
  background-color: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(231, 76, 60, 0.2);
}

/* External Link Form */
.link-form {
  max-width: 600px;
  margin: 0 auto 2rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.link-form input {
  padding: 0.75rem 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
}

.link-form input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  outline: none;
  background-color: #fff;
}

.link-form button {
  padding: 0.75rem 1.5rem;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  align-self: center;
  margin-top: 0.5rem;
}

.link-form button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #95a5a6;
  font-style: italic;
}

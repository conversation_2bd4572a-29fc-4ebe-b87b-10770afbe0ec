/* Add navbar styling to match other components */
.navbar {
  min-width: fit-content;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 32px;
  background-color: #ffffff;
  color: rgb(0, 0, 0);
  border-radius: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.navbar h1 {
  font-size: 24px;
  margin: 0;
  color: rgb(0, 0, 0);
}

.navbar nav a {
  margin-left: 16px;
  padding: 6px 14px;
  color: #000000;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.branding {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logout-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  margin-left: 1rem;
  border-radius: 5px;
  cursor: pointer;
  width: auto;
}

.logout-btn:hover {
  background-color: #c0392b;
}

/* Main container with consistent styling */
.forum-post-container {
  max-width: 800px;
  margin: 32px auto;
  padding: 24px;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: transform 0.2s ease;
}

.forum-post-container:hover {
  transform: translateY(-2px);
}

.forum-post-container h2 {
  font-size: 1.6rem;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
  position: relative;
}

.forum-post-container h2:after {
  content: "";
  display: block;
  width: 50px;
  height: 3px;
  background-color: #9b59b6;
  margin: 8px auto 0;
  border-radius: 2px;
}

/* Form inputs styling to match Dashboard */
.forum-post-container input[type="text"],
.forum-post-container input[type="file"],
.forum-post-container textarea {
  width: 100%;
  padding: 12px 14px;
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background-color: #f8f9fa;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.forum-post-container input[type="text"]:focus,
.forum-post-container input[type="file"]:focus,
.forum-post-container textarea:focus {
  border-color: #9b59b6;
  box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.2);
  outline: none;
}

textarea {
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
}

/* Primary button styling to match Dashboard */
.forum-post-container button {
  padding: 12px 20px;
  font-size: 14px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(155, 89, 182, 0.2);
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-top: 10px;
  margin-right: 10px;
}

.forum-post-container button:hover {
  background: linear-gradient(135deg, #8e44ad, #7d3c98);
  box-shadow: 0 6px 15px rgba(155, 89, 182, 0.3);
  transform: translateY(-2px);
}

.post {
  margin-top: 24px;
  margin-bottom: 24px;
  padding: 20px;
  border-radius: 12px;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
}


.post h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: #111827;
}

.post p {
  font-size: 14px;
  color: #374151;
  margin: 6px 0;
}

.post img {
  margin-top: 10px;
  max-width: 100%;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.post button {
  margin-top: 10px;
  background-color: #f3f4f6;
  color: #1f2937;
  border: 1px solid #d1d5db;
}

h4 {
  margin-top: 40px;
  font-size: 16px;
  color: #111827;
}

ul {
  list-style: none;
  padding: 0;
  margin: 16px 0;
}

ul li {
  padding: 10px 0;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
}

ul li strong {
  margin-right: 6px;
  color: #1f2937;
}

input[placeholder="Add a comment"] {
  margin-top: 12px;
  padding: 10px;
  width: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background-color: #f9fafb;
}

.title-input {
  margin-top: auto;
  margin-bottom: 10px;
  padding: 10px;
  width: 25%;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background-color: #f9fafb;
}
//to prevent entering of dashboard directly
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from './firebase'; // adjust path if needed

const PrivateRoute = ({ children }) => {
  const [user, loading] = useAuthState(auth);

  if (loading) {
    return <p>Loading...</p>; // Show while checking auth
  }

  return user ? children : <Navigate to="/login" replace />;
};

export default PrivateRoute;
